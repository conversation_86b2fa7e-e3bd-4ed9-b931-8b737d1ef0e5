/**
 * Enhanced Pipeline Modal Component
 * Full-screen modal for multi-step document processing with live console logs
 * Supports both full-screen modal and right-side panel layouts
 */

import React, { useState, useEffect, useRef } from 'react';
import { X, Maximize2, Minimize2, Play, Pause, RotateCcw, PanelRight, Fullscreen } from 'lucide-react';
import PipelineStepCard from './PipelineStepCard.jsx';
import LiveConsoleLogger from './LiveConsoleLogger.jsx';
import { PIPELINE_STEPS } from '../../../core/config/pipelineSteps.js';

const PipelineModal = ({
  isOpen,
  onClose,
  file,
  onProcessingComplete,
  processingService
}) => {
  const [isFullscreen, setIsFullscreen] = useState(true);
  const [currentStep, setCurrentStep] = useState(null);
  const [stepResults, setStepResults] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [consoleLogs, setConsoleLogs] = useState([]);
  const [processingStartTime, setProcessingStartTime] = useState(null);

  const modalRef = useRef(null);
  const consoleRef = useRef(null);

  // Auto-scroll console to bottom
  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [consoleLogs]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && !isProcessing) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, isProcessing, onClose]);

  // Add console log entry
  const addConsoleLog = (level, message, data = null) => {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      step: currentStep
    };

    setConsoleLogs(prev => [...prev, logEntry]);
  };

  // Start processing pipeline
  const startProcessing = async () => {
    if (!file || !processingService) {
      addConsoleLog('error', 'No file or processing service available');
      return;
    }

    setIsProcessing(true);
    setIsPaused(false);
    setProcessingStartTime(Date.now());
    setStepResults({});
    setOverallProgress(0);

    addConsoleLog('info', `🚀 Starting pipeline processing for: ${file.name}`);
    addConsoleLog('info', `📁 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`);

    try {
      // Process through each step
      for (let i = 0; i < PIPELINE_STEPS.length; i++) {
        const step = PIPELINE_STEPS[i];

        if (isPaused) {
          addConsoleLog('warning', '⏸️ Processing paused by user');
          break;
        }

        setCurrentStep(step.id);
        addConsoleLog('info', `🔄 Starting step ${i + 1}/${PIPELINE_STEPS.length}: ${step.name}`);

        const stepStartTime = Date.now();

        try {
          // Execute step based on type
          let stepResult;

          switch (step.id) {
            case 'pdf_extraction':
              stepResult = await processingService.runPdfExtraction(file, {
                progressCallback: (progress) => {
                  addConsoleLog('debug', `📄 PDF extraction progress: ${progress}%`);
                }
              });
              break;

            case 'deepseek_analysis_1':
              const pdfResult = stepResults.pdf_extraction;
              if (!pdfResult?.success) {
                throw new Error('PDF extraction required for DeepSeek analysis');
              }
              stepResult = await processingService.runDeepSeekAnalysis(pdfResult.data.text, {
                apiKey: await getApiKey(),
                language: 'pol',
                companyInfo: await getCompanyInfo()
              });
              break;

            case 'rag_enhancement':
              stepResult = await processingService.runRAGEnhancement(file, {
                progressCallback: (progress) => {
                  addConsoleLog('debug', `🔗 RAG enhancement progress: ${progress}%`);
                }
              });
              break;

            case 'tesseract_reference':
              stepResult = await processingService.runTesseractReference(file, {
                language: stepResults.deepseek_analysis_1?.data?.language || 'pol',
                progressCallback: (progress) => {
                  addConsoleLog('debug', `👁️ OCR processing progress: ${progress}%`);
                }
              });
              break;

            case 'deepseek_analysis_2':
            case 'deepseek_analysis_3':
              // These would need specific implementations
              stepResult = {
                success: true,
                stepId: step.id,
                data: { message: 'Enhanced analysis step - implementation pending' },
                timing: 1000
              };
              break;

            case 'final_output':
              stepResult = await processingService.runFinalOutput(file, stepResults, {});
              break;

            default:
              throw new Error(`Unknown step: ${step.id}`);
          }

          const stepDuration = Date.now() - stepStartTime;

          if (stepResult.success) {
            setStepResults(prev => ({ ...prev, [step.id]: stepResult }));
            addConsoleLog('success', `✅ Step completed in ${stepDuration}ms: ${step.name}`);

            if (stepResult.data) {
              addConsoleLog('debug', `📊 Step output size: ${JSON.stringify(stepResult.data).length} characters`);
            }
          } else {
            throw new Error(stepResult.error || 'Step failed');
          }

        } catch (stepError) {
          addConsoleLog('error', `❌ Step failed: ${step.name} - ${stepError.message}`);
          setStepResults(prev => ({
            ...prev,
            [step.id]: {
              success: false,
              error: stepError.message,
              stepId: step.id
            }
          }));
        }

        // Update overall progress
        const progress = ((i + 1) / PIPELINE_STEPS.length) * 100;
        setOverallProgress(progress);
      }

      const totalDuration = Date.now() - processingStartTime;
      addConsoleLog('success', `🎉 Pipeline processing completed in ${(totalDuration / 1000).toFixed(2)}s`);

      if (onProcessingComplete) {
        onProcessingComplete(stepResults);
      }

    } catch (error) {
      addConsoleLog('error', `💥 Pipeline processing failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
      setCurrentStep(null);
    }
  };

  // Helper functions
  const getApiKey = async () => {
    // Get API key from settings or environment
    return process.env.DEEPSEEK_API_KEY || '';
  };

  const getCompanyInfo = async () => {
    // Get company info from settings
    return {
      name: process.env.COMPANY_NAME || '',
      nip: process.env.COMPANY_NIP || ''
    };
  };

  // Reset pipeline
  const resetPipeline = () => {
    setStepResults({});
    setCurrentStep(null);
    setOverallProgress(0);
    setConsoleLogs([]);
    setIsProcessing(false);
    setIsPaused(false);
  };

  if (!isOpen) { return null; }

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
      <div
        ref={modalRef}
        className={`bg-white rounded-lg shadow-2xl flex flex-col ${
          isFullscreen
            ? 'w-full h-full m-4'
            : 'w-11/12 h-5/6 max-w-7xl'
        }`}
      >
        {/* Modal Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <h2 className="text-lg font-semibold text-gray-900">
              Multi-Step Processing Pipeline
            </h2>
            {file && (
              <span className="text-sm text-gray-600 truncate max-w-xs">
                Processing: {file.name}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* Control buttons */}
            <button
              onClick={startProcessing}
              disabled={isProcessing || !file}
              className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              <Play size={16} />
              <span>Start</span>
            </button>

            <button
              onClick={() => setIsPaused(!isPaused)}
              disabled={!isProcessing}
              className="flex items-center space-x-1 px-3 py-1 bg-yellow-600 text-white rounded hover:bg-yellow-700 disabled:opacity-50"
            >
              <Pause size={16} />
              <span>Pause</span>
            </button>

            <button
              onClick={resetPipeline}
              disabled={isProcessing}
              className="flex items-center space-x-1 px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
            >
              <RotateCcw size={16} />
              <span>Reset</span>
            </button>

            {/* Fullscreen toggle */}
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={20} />}
            </button>

            {/* Close button */}
            <button
              onClick={onClose}
              disabled={isProcessing}
              className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Overall Progress */}
        <div className="px-4 py-2 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-600">{Math.round(overallProgress)}%</span>
          </div>
          <div className="w-full bg-gray-300 rounded-full h-1.5">
            <div
              className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>

        {/* Main Content - Split Layout */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Side - Pipeline Steps */}
          <div className="w-2/5 border-r border-gray-200 overflow-y-auto">
            <div className="p-3">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Pipeline Steps</h3>
              <div className="space-y-3">
                {PIPELINE_STEPS.map((step, index) => (
                  <PipelineStepCard
                    key={step.id}
                    step={step}
                    stepNumber={index + 1}
                    isActive={currentStep === step.id}
                    isCompleted={stepResults[step.id]?.success}
                    isFailed={stepResults[step.id] && !stepResults[step.id].success}
                    result={stepResults[step.id]}
                    onRerun={() => {
                      // Implement individual step rerun
                      addConsoleLog('info', `🔄 Rerunning step: ${step.name}`);
                    }}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - Live Console */}
          <div className="w-3/5 flex flex-col min-h-0">
            <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
              <h3 className="text-lg font-medium text-gray-900">Live Console</h3>
            </div>
            <div className="flex-1 min-h-0">
              <LiveConsoleLogger
                logs={consoleLogs}
                isProcessing={isProcessing}
                onClear={() => setConsoleLogs([])}
                className="h-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PipelineModal;
